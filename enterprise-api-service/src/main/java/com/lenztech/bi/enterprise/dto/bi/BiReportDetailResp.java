package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

import java.util.List;

/**
 * @Description:详情请求响应信息
 * @Author: z<PERSON><PERSON>e
 * @Date: 3/18/20 AM10:30
 */
@Data
public class BiReportDetailResp {

    /**
     * 访店日期
     */
    private String visitDate;

    /**
     * 拜访账号
     */
    private String account;

    /**
     * 拜访门店
     */
    private String storeName;

    /**
     * 进店时间
     */
    private String enterTime;

    /**
     * 出店时间
     */
    private String leaveTime;

    /**
     * 用时
     */
    private String operatingTime;

    /**
     * 隔天数
     */
    private int otherDayNum;

    /**
     * tab标签
     */
    private List<BiReportDetailTab> tabList;

    /**
     * kpi
     */
    private List<BiReportDetailKpiGroup> kpiGroupList;

    /**
     * sku
     */
    private List<BiReportDetailSkuGroup> skuGroupList;


}
