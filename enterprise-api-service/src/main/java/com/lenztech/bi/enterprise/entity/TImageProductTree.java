package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;

/**
 * <p>
 * 图像模型树形结构
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class TImageProductTree extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商品名
     */
    private String name;

    /**
     * 商品英文名
     */
    private String eName;

    /**
     * 父id
     */
    private String parentId;

    /**
     * 父id
     */
    private String parentIdNew;

    /**
     * 品类category, 子品类subcategory, 品牌brand, 系列series, sku
     */
    private String type;

    /**
     * 商品唯一编号
     */
    private String productNum;

    /**
     * 识别程序是否能处理
     */
    private Integer isAccomplish;

    /**
     * 示例图url
     */
    private String imageUrl;

    /**
     * 插入时间
     */
    private LocalDateTime insertTime;

    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;

    /**
     * SKU宽（厘米）
     */
    private Double width;

    /**
     * SKU高（厘米）
     */
    private Double height;

    /**
     * SKU可能的最低价格（元）
     */
    private Double minPrice;

    /**
     * SKU可能的最高价格（元）
     */
    private Double maxPrice;

    /**
     * SKU在本系列中的出现频率
     */
    private Double freq;

    /**
     * SKU出现在中间层的概率
     */
    private Double probNotMid;

    /**
     * SKU是否需要运行PSKU识别程序 0否 1是
     */
    private String needPskuRecog;

    /**
     * sku规格 200 400 750等
     */
    private Integer size;

    /**
     * 第三方门店id
     */
    private String storeId;

    /**
     * 客户给出的最原始名称
     */
    private String originalName;

    /**
     * 全国统一的商品69码
     */
    private String barcode;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 是否有效 0无效 1有效
     */
    private Boolean ifValid;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String geteName() {
        return eName;
    }

    public void seteName(String eName) {
        this.eName = eName;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getParentIdNew() {
        return parentIdNew;
    }

    public void setParentIdNew(String parentIdNew) {
        this.parentIdNew = parentIdNew;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getProductNum() {
        return productNum;
    }

    public void setProductNum(String productNum) {
        this.productNum = productNum;
    }

    public Integer getIsAccomplish() {
        return isAccomplish;
    }

    public void setIsAccomplish(Integer isAccomplish) {
        this.isAccomplish = isAccomplish;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public LocalDateTime getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(LocalDateTime insertTime) {
        this.insertTime = insertTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getHeight() {
        return height;
    }

    public void setHeight(Double height) {
        this.height = height;
    }

    public Double getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(Double minPrice) {
        this.minPrice = minPrice;
    }

    public Double getMaxPrice() {
        return maxPrice;
    }

    public void setMaxPrice(Double maxPrice) {
        this.maxPrice = maxPrice;
    }

    public Double getFreq() {
        return freq;
    }

    public void setFreq(Double freq) {
        this.freq = freq;
    }

    public Double getProbNotMid() {
        return probNotMid;
    }

    public void setProbNotMid(Double probNotMid) {
        this.probNotMid = probNotMid;
    }

    public String getNeedPskuRecog() {
        return needPskuRecog;
    }

    public void setNeedPskuRecog(String needPskuRecog) {
        this.needPskuRecog = needPskuRecog;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode;
    }

    public Boolean getIfValid() {
        return ifValid;
    }

    public void setIfValid(Boolean ifValid) {
        this.ifValid = ifValid;
    }
}
