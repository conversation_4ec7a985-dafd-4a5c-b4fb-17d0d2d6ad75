package com.lenztech.bi.enterprise.utils.date;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public enum DateTimeString {

    /**
     * 格式化时间,使用':'作为间隔. 例如13点02分35秒表示为'13:02:35'
     */
    TIME(true, "HH:mm:ss"),

    /**
     * 格式化日期,使用'-'作为间隔. 例如2018年3月13日表示为'2018-03-13'
     */
    DATE(true, "yyyy-MM-dd"),

    /**
     * 格式化使用汉字做分割 2019年08月08日
     */
    DATE_ZH(true, "yyyy年MM月dd日"),

    /**
     * 格式化日期部分,但是年月日之间没有任何分隔符号. 例如2018年3月13日表示为'20180313'
     */
    DATE_NONE_SEPARATOR(true, "yyyyMMdd"),

    /**
     * 格式化日期和时间,日期部分使用'-'作为间隔,时间部分使用':'作为间隔.日期和时间部分使用' '连接
     * 例如2018年3月13日13点02分35秒表示为'2018-03-13 13:02:35'
     */
    DATE_TIME(true, "yyyy-MM-dd HH:mm:ss"),

    /**
     * 格式化日期没有分隔符
     */
    DATE_TIME_NO_SPLIT(true, "yyyyMMddHHmmss"),

    /**
     * 格式化日期和时间，使用汉字分隔 `2019年08月08号 08时08分08秒`
     */
    DATE_TIME_ZH(true, "yyyy年MM月dd日 HH时mm分ss秒"),

    /**
     * 格式化日期和时间和毫秒,日期部分使用'-'作为间隔,时间部分使用':'作为间隔.日期和时间部分使用' '连接,毫秒部分使用','连接
     * 例如2018年3月13日13点02分35秒213毫秒表示为'2018-03-13 13:02:35,213'
     */
    DATE_TIME_MILLIS(true, "yyyy-MM-dd HH:mm:ss,SSS"),

    /**
     * yyyyMMddHHmmssSSS  没有分隔符
     */
    DATE_TIME_MILLIS_NO_SPLIT(true, "yyyyMMddHHmmssSSS"),

    /**
     * 格式化日期和时间和毫秒和时区,日期部分使用'-'作为间隔,时间部分使用':'作为间隔.日期和时间部分使用' '连接,毫秒部分使用','连接,时区部分使用' '连接
     * 例如北京时间2018年3月13日13点02分35秒213毫秒表示为'2018-03-13 13:02:35,213 +0800'
     */
    DATE_TIME_MILLIS_ZONE(false, "yyyy-MM-dd HH:mm:ss,SSS Z");

    private final boolean local;

    private final DateTimeFormatter formatter;

    DateTimeString(final boolean local, final String formatterString) {
        this.local = local;
        this.formatter = DateTimeFormatter.ofPattern(formatterString);
    }

    public final String toString(final LocalDateTime localDateTime) {
        if (local) {
            return localDateTime.format(formatter);
        } else {
            return localDateTime.atZone(ZoneId.systemDefault()).format(formatter);
        }
    }

    public final String toString(final Date date) {
        return toString(DateTimeTool.toLocalDateTime(date));
    }

    public final LocalDateTime toDateTime(final CharSequence dateTimeCharSequence) {
        if (this == DateTimeString.DATE || this == DateTimeString.DATE_NONE_SEPARATOR) {
            return LocalDate.parse(dateTimeCharSequence, this.formatter).atStartOfDay();
        }
        if (local) {
            return LocalDateTime.parse(dateTimeCharSequence, formatter);
        } else {
            return ZonedDateTime.parse(dateTimeCharSequence, formatter).withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
        }
    }

    public final Date toUtilDate(final CharSequence dateTimeCharSequence) {
        return DateTimeTool.toUtilDate(toDateTime(dateTimeCharSequence));
    }

    public DateTimeFormatter getFormatter() {
        return formatter;
    }
}
