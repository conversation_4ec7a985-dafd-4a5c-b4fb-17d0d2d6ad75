package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 单份答卷表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
@Data
public class TResponse extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 操作id
     */
    @TableId("Id")
    private String Id;

    /**
     * 任务id
     */
    private String taskidOwner;

    /**
     * 子任务
     */
    private String taskid;

    /**
     * 答题人手机号
     */
    private String phone;

    /**
     * 开始答题时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    private String position;

    /**
     * 0未审核 1合格 2不合格 4标记数据 9一审合格二审待审
     */
    private String status;

    @TableField("uId")
    private String uId;

    @TableField("GPS")
    private String gps;

    /**
     * 一审id
     */
    private String actid1;

    /**
     * 二审id
     */
    private String actid2;

    /**
     * 是否被修改 0否，1是
     */
    private String alterstatus;

    /**
     * 一审修改时间
     */
    private LocalDateTime altertime1;

    /**
     * 二审修改时间
     */
    private LocalDateTime altertime2;

    /**
     * 0待审 1合格-待审 2不合格 3合格-合格 4合格-不合格
     */
    private String nowstatus;

    /**
     * GPS字段的point类
     */
    private Object rangevalue;

    /**
     * 任务审核标记
     */
    private String tips;

    /**
     * 是否审核时间到期 0未到期 1已到期
     */
    private String shdq;

    /**
     * 申诉未处理到期 0默认 1允许自动审核
     */
    private String sswcldq;

    private String fen;

    private String fen1;

    private String fen2;

    private LocalDateTime upTime;

    private Double percent;

    private Double rewardOld;

    @TableField("doorGPS")
    private String doorGPS;

    @TableField("doorAddress")
    private String doorAddress;

    /**
     * 答题时任务是否开启倾斜角判断 0否 1是
     */
    private String ifobliqueTask;

    /**
     * 会员是否启用倾斜角判断 0否 1是
     */
    private String ifobliqueUser;
    private Byte storeStatus;
}
