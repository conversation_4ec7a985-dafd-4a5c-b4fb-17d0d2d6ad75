package com.lenztech.bi.enterprise.dto.car;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 14:01
 **/

@Data
public class PostCodeDTO {
     private String province;
     private String city;
     private String address;
     private Integer postCodeQuantity;
     private Integer responseTranslateNum;
     private Integer postCodeTranslateNum;
     private Double postCodeTranslateRate;
     private Integer scanCodeNum;
     private Double scanCodeRate;
     private Integer peopleNum;
     private Integer IPZShopping;
     private Integer fission;
     private Integer other;
     private List<String> coordinate;
     private List<String> addressList;
}
