package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * PG RTIR 主数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_pg_rtir_md")
public class PgRtirMd extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 客户产品编码，对应trax ai识别的barcode
     */
    @TableId(value = "gtin_code", type = IdType.INPUT)
    private String gtinCode;

    /**
     * 客户字段
     */
    private String fpcCode;

    /**
     * 客户字段
     */
    private String itemStatus;

    /**
     * 客户字段
     */
    private String year;

    /**
     * 客户字段
     */
    private String curDate;

    /**
     * 客户字段
     */
    private String sosDate;

    /**
     * 客户品类编码
     */
    private String categoryCode;

    /**
     * 客户品类名称
     */
    private String categoryCn;

    /**
     * 客户商品编码
     */
    private String productFormCode;

    /**
     * 客户商品名称
     */
    private String productFormCn;

    /**
     * 客户品牌编码
     */
    private String brandCode;

    /**
     * 客户品牌名称
     */
    private String brandCn;

    /**
     * 客户系列编码
     */
    private String variantCode;

    /**
     * 客户系列名称
     */
    private String variantCn;

    /**
     * 客户商品全名
     */
    private String productNameCn;

    /**
     * 长
     */
    private String length;

    /**
     * 宽
     */
    private String width;

    /**
     * 高
     */
    private String height;

    /**
     * 多支装计算方法
     */
    private String mutiCalculation;

    /**
     * 是否建模（1代表已建模）
     */
    private String modeling;

    /**
     * 建议价格
     */
    private String suggestPrice;

    /**
     * 是否本品（1是本品，0是竞品）
     */
    private String isPgProduct;

    /**
     * 建模日期
     */
    private String modelingDate;

    /**
     * 是否POSM（1代表是，0代表否）
     */
    private String isPosm;

    /**
     * barcode是否Trax新建（1代表是，0代表否）
     */
    private String isTraxCreate;
}
