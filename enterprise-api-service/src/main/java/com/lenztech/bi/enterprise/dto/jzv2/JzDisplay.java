package com.lenztech.bi.enterprise.dto.jzv2;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (JzDisplay)实体类
 *
 * <AUTHOR>
 * @since 2021-10-11 16:11:29
 */
@Data
public class JzDisplay implements Serializable {
    private static final long serialVersionUID = -18482277242575078L;

    private Object id;
    /**
     * response_id
     */
    private String responseId;
    /**
     * 图片id
     */
    private String imgId;
    /**
     * 场景名称
     */
    private String sceneName;
    /**
     * 场景编号
     */
    private Integer sceneSeq;
    /**
     * 地堆面积
     */
    private Double area;
    /**
     * 江中产品占比
     */
    private Double ratio;
    /**
     * 是否有重复图片
     */
    private Integer ifDuplicate;
    /**
     * 重复图片id
     */
    private String duplicateImgId;
    /**
     * 更新时间
     */
    private Date updateTime;

}