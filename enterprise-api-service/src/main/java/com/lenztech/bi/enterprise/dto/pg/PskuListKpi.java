package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2023/9/18
 * Time: 10:43
 * 类功能:
 */
@Data
public class PskuListKpi {

    private Integer id;
    private String category = "";
    private String kpiName = "";
    private String kpiType = "";
    private String kpiValue = "";
    /**
     * 客户要求如此命名
     */
    @JsonProperty("SKUdetails")
    private List<PskuListKpiSkuDetails> skuDetails = new ArrayList<>();

}
