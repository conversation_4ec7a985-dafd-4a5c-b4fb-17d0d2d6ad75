package com.lenztech.bi.enterprise.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 价签结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
public class UnileverPricetag extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 是否分销(是1/否0)
     */
    private Integer ifDist;

    /**
     * 识别价格
     */
    private BigDecimal price;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public Integer getIfDist() {
        return ifDist;
    }

    public void setIfDist(Integer ifDist) {
        this.ifDist = ifDist;
    }
    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public String toString() {
        return "UnileverPricetag{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", productName=" + productName +
        ", ifDist=" + ifDist +
        ", price=" + price +
        "}";
    }
}
