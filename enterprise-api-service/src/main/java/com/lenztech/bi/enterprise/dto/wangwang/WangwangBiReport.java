package com.lenztech.bi.enterprise.dto.wangwang;

import lombok.Data;

/**
 * 旺旺bi结果集
 *
 * <AUTHOR>
 * @date 2020-11-10 17:02:02
 */
@Data
public class WangwangBiReport {

    private String id;

    /**答卷id**/
    private String responseId;

    /**图片组id**/
    private String groupId;

    /**拼接原始图片id集合**/
    private String sourceImgIdList;

    /**拼接结果效果图url**/
    private String stitchImgUrl;

    /**图片url**/
    private String imgUrl;

    /**识别图url**/
    private String recUrl;

    /**图片id**/
    private String imgId;

    /**图片长（高）**/
    private Integer imgHeight;

    /**图片宽度**/
    private Integer imgWidth;

    /**货架高度**/
    private Integer shelfHeight;

    /**货架层数**/
    private Integer numLayers;

    /**识别出sku数量**/
    private Integer numPatchs;

    /**是否翻拍**/
    private Integer isRemake;

    /**翻拍分值**/
    private Double remakeScore;

    /**sku代码**/
    private String productId;

    /**sku名称**/
    private String productName;

    /**图片坐标**/
    private String coordinate;

    /**图片识别高度**/
    private Integer patchHeight;

    /**图片识别宽度**/
    private Integer patchWidth;

    /**所在层**/
    private Integer layer;

    /**所在列**/
    private Integer column;

    /**场景**/
    private String scene;

    /**是否排面**/
    private Integer ifFacing;

    /**sku所占排面数**/
    private Integer facingCount;

    /****/
    private java.util.Date updateTime;

}
