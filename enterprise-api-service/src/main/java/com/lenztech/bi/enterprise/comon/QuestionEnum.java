package com.lenztech.bi.enterprise.comon;

/**
 * 南区北区
 * <AUTHOR>
 * @version V1.0
 * @date 2020/04/06 15:45
 * @since JDK 1.8
 */
public enum QuestionEnum {

    STOCK_OUT("缺货"),

    LOW_FACE("低排面"),

    ERROR_PRICE("价格错误"),
    ;

    private String question;

    QuestionEnum(String question) {
        this.question = question;
    }

    public String getQuestion() {
        return question;
    }
}
