package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import com.lenztech.bi.enterprise.utils.json.DataSerializerUtils;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-22
 */
public class UnileverDisplayPosm extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 问卷ID
     */
    private String responseId;

    /**
     * 图片ID
     */
    private String imgId;

    /**
     * 场景
     */
    private String scene;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 地堆宽
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double width;

    /**
     * 地堆长
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double length;

    /**
     * 面积
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double area;

    /**
     * 图片链接
     */
    private String imgUrl;

    /**
     * 场景数量(仅对非地堆场景有效)
     */
    private Integer count;

    /**
     * 奥妙地堆面积
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double omoArea;

    /**
     * 金纺地堆面积
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double cftArea;

    /**
     * 花木星球地堆面积
     */
    @JsonSerialize(using = DataSerializerUtils.class)
    private Double lhpArea;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getImgId() {
        return imgId;
    }

    public void setImgId(String imgId) {
        this.imgId = imgId;
    }
    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }
    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }
    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }
    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }
    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getOmoArea() {
        return omoArea;
    }

    public void setOmoArea(Double omoArea) {
        this.omoArea = omoArea;
    }

    public Double getCftArea() {
        return cftArea;
    }

    public void setCftArea(Double cftArea) {
        this.cftArea = cftArea;
    }

    public Double getLhpArea() {
        return lhpArea;
    }

    public void setLhpArea(Double lhpArea) {
        this.lhpArea = lhpArea;
    }

    @Override
    public String toString() {
        return "UnileverDisplayPosm{" +
                "id=" + id +
                ", responseId='" + responseId + '\'' +
                ", imgId='" + imgId + '\'' +
                ", scene='" + scene + '\'' +
                ", brand='" + brand + '\'' +
                ", width=" + width +
                ", length=" + length +
                ", area=" + area +
                ", imgUrl='" + imgUrl + '\'' +
                ", count=" + count +
                ", omoArea=" + omoArea +
                ", cftArea=" + cftArea +
                ", lhpArea=" + lhpArea +
                '}';
    }
}
