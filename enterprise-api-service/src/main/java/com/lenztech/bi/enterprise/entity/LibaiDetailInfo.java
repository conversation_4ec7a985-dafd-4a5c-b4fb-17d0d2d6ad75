package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (LibaiDetailInfo)实体类
 *
 * <AUTHOR>
 * @since 2022-01-27 11:57:58
 */
@Data
public class LibaiDetailInfo implements Serializable {
    private static final long serialVersionUID = 553430042963080385L;
    /**
     * 主键
     */
    private Integer id;
    /**
     * 答卷ID
     */
    private String responseId;
    /**
     * 图片ID
     */
    private String imageId;
    /**
     * 商品ID
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 牌面数
     */
    private Integer facingCount;
    /**
     * 是否分销 0. 未分销 1. 有分销
     */
    private Integer distributeStatus;
    /**
     * 创建时间
     */
    private Date createTime;

}