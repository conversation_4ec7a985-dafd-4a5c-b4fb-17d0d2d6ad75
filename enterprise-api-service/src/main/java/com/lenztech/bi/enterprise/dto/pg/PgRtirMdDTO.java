package com.lenztech.bi.enterprise.dto.pg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * PG RTIR 主数据 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel("PG RTIR 主数据 DTO")
public class PgRtirMdDTO {

    /**
     * 客户产品编码，对应trax ai识别的barcode
     */
    @ApiModelProperty("客户产品编码，对应trax ai识别的barcode")
    private String gtinCode;

    /**
     * 客户字段
     */
    @ApiModelProperty("客户字段")
    private String fpcCode;

    /**
     * 客户字段
     */
    @ApiModelProperty("客户字段")
    private String itemStatus;

    /**
     * 客户字段
     */
    @ApiModelProperty("客户字段")
    private String year;

    /**
     * 客户字段
     */
    @ApiModelProperty("客户字段")
    private String curDate;

    /**
     * 客户字段
     */
    @ApiModelProperty("客户字段")
    private String sosDate;

    /**
     * 客户品类编码
     */
    @ApiModelProperty("客户品类编码")
    private String categoryCode;

    /**
     * 客户品类名称
     */
    @ApiModelProperty("客户品类名称")
    private String categoryCn;

    /**
     * 客户商品编码
     */
    @ApiModelProperty("客户商品编码")
    private String productFormCode;

    /**
     * 客户商品名称
     */
    @ApiModelProperty("客户商品名称")
    private String productFormCn;

    /**
     * 客户品牌编码
     */
    @ApiModelProperty("客户品牌编码")
    private String brandCode;

    /**
     * 客户品牌名称
     */
    @ApiModelProperty("客户品牌名称")
    private String brandCn;

    /**
     * 客户系列编码
     */
    @ApiModelProperty("客户系列编码")
    private String variantCode;

    /**
     * 客户系列名称
     */
    @ApiModelProperty("客户系列名称")
    private String variantCn;

    /**
     * 客户商品全名
     */
    @ApiModelProperty("客户商品全名")
    private String productNameCn;

    /**
     * 长
     */
    @ApiModelProperty("长")
    private String length;

    /**
     * 宽
     */
    @ApiModelProperty("宽")
    private String width;

    /**
     * 高
     */
    @ApiModelProperty("高")
    private String height;

    /**
     * 多支装计算方法
     */
    @ApiModelProperty("多支装计算方法")
    private String mutiCalculation;

    /**
     * 是否建模（1代表已建模）
     */
    @ApiModelProperty("是否建模（1代表已建模）")
    private String modeling;

    /**
     * 建议价格
     */
    @ApiModelProperty("建议价格")
    private String suggestPrice;

    /**
     * 是否本品（1是本品，0是竞品）
     */
    @ApiModelProperty("是否本品（1是本品，0是竞品）")
    private String isPgProduct;

    /**
     * 建模日期
     */
    @ApiModelProperty("建模日期")
    private String modelingDate;

    /**
     * 是否POSM（1代表是，0代表否）
     */
    @ApiModelProperty("是否POSM（1代表是，0代表否）")
    private String isPosm;

    /**
     * barcode是否Trax新建（1代表是，0代表否）
     */
    @ApiModelProperty("barcode是否Trax新建（1代表是，0代表否）")
    private String isTraxCreate;
}
