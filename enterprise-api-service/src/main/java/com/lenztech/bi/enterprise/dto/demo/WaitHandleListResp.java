package com.lenztech.bi.enterprise.dto.demo;

import lombok.Data;

import java.util.List;

@Data
public class WaitHandleListResp {

    private List<WaitHandle> waitHandleList;

    @Data
    public static class WaitHandle {
        private String categoryId;
        private String submitId;
        private String categoryName;
        private String reportTime;
        private String needHandleTaskNum;
        private String coreProductNum;
    }
}
