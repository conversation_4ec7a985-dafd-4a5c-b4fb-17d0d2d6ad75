package com.lenztech.bi.enterprise.dto.perfetti;

import lombok.Data;

/**
 * @ClassName PerfeittiPatch
 * @Description: TODO
 * <AUTHOR>
 * @Date 2021/9/24
 **/

@Data
public class PerfeittiPatch {
    private String id;

    /**
     * sku代码
     */
    private String skuCode;

    private String skuName;

    /**
     * sku在图片中的坐标位置
     */
    private String coordinate;

    private Integer patchHeight;

    private Integer patchWidth;

    /**
     * 所在层
     */
    private Integer layer;

    /**
     * 所在层
     */
    private Integer column;

    /**
     * 所在层
     */
    private Integer inrange;

    private String scene;

    /**
     * 是否面位 1是 0否
     */
    private boolean facing;

    /**
     * 是否面位 1是 0否
     */
    private Integer facingCount;

}
