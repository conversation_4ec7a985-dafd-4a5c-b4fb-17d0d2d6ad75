package com.lenztech.bi.enterprise.entity;

public class FreezerInspect {
    private String assetCode;
    private String layers;
    private String remarks;

    // 构造函数
    public FreezerInspect() {}

    public FreezerInspect(String assetCode, String layers, String remarks) {
        this.assetCode = assetCode;
        this.layers = layers;
        this.remarks = remarks;
    }

    // getter 和 setter 方法
    public String getAssetCode() {
        return assetCode;
    }

    public void setAssetCode(String assetCode) {
        this.assetCode = assetCode;
    }

    public String getLayers() {
        return layers;
    }

    public void setLayers(String layers) {
        this.layers = layers;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
} 