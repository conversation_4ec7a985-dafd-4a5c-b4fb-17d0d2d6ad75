package com.lenztech.bi.enterprise.entity;

import java.util.List;

public class InspectRequest {
    private String requestId;
    private Integer type;
    private String inspectAddress;
    private String inspectDate;
    private String inspectPhone;
    private String customerType;
    private String freezerNumber;
    private List<InspectResult> inspectResultList;
    private List<FreezerInspect> freezerInspectList;
    private String inspectStoreName;
    private String inspectStoreResult;
    private String inspectTime;
    private String link;
    private String remarks;
    private String storeCode;
    private String wordOrderId;
    private String yearMonths;

    // 构造函数
    public InspectRequest() {}

    // getter 和 setter 方法
    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getInspectAddress() {
        return inspectAddress;
    }

    public void setInspectAddress(String inspectAddress) {
        this.inspectAddress = inspectAddress;
    }

    public String getInspectDate() {
        return inspectDate;
    }

    public void setInspectDate(String inspectDate) {
        this.inspectDate = inspectDate;
    }

    public String getInspectPhone() {
        return inspectPhone;
    }

    public void setInspectPhone(String inspectPhone) {
        this.inspectPhone = inspectPhone;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public String getFreezerNumber() {
        return freezerNumber;
    }

    public void setFreezerNumber(String freezerNumber) {
        this.freezerNumber = freezerNumber;
    }

    public List<InspectResult> getInspectResultList() {
        return inspectResultList;
    }

    public void setInspectResultList(List<InspectResult> inspectResultList) {
        this.inspectResultList = inspectResultList;
    }

    public List<FreezerInspect> getFreezerInspectList() {
        return freezerInspectList;
    }

    public void setFreezerInspectList(List<FreezerInspect> freezerInspectList) {
        this.freezerInspectList = freezerInspectList;
    }

    public String getInspectStoreName() {
        return inspectStoreName;
    }

    public void setInspectStoreName(String inspectStoreName) {
        this.inspectStoreName = inspectStoreName;
    }

    public String getInspectStoreResult() {
        return inspectStoreResult;
    }

    public void setInspectStoreResult(String inspectStoreResult) {
        this.inspectStoreResult = inspectStoreResult;
    }

    public String getInspectTime() {
        return inspectTime;
    }

    public void setInspectTime(String inspectTime) {
        this.inspectTime = inspectTime;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getWordOrderId() {
        return wordOrderId;
    }

    public void setWordOrderId(String wordOrderId) {
        this.wordOrderId = wordOrderId;
    }

    public String getYearMonths() {
        return yearMonths;
    }

    public void setYearMonths(String yearMonths) {
        this.yearMonths = yearMonths;
    }
} 