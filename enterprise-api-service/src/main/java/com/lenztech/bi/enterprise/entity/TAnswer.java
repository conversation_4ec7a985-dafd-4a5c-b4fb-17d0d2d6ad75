package com.lenztech.bi.enterprise.entity;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 答案
 * @Date 2020/9/10 18:44
 **/
@Data
public class TAnswer {
    private String id;
    private String responseId;
    private String qid;
    private String questionTempId;
    private String answer;
    private Integer version;
    private String image;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
