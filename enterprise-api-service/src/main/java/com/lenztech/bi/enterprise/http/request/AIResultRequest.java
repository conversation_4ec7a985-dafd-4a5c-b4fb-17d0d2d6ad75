package com.lenztech.bi.enterprise.http.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("AI识别结果查询请求参数")
public class AIResultRequest implements Serializable {

    @ApiModelProperty("企业ID")
    private String companyId;

    @ApiModelProperty("答卷ID")
    private String responseId;

    @ApiModelProperty("企业自定义业务参数")
    private String[] businessDataParamList;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }

    public String[] getBusinessDataParamList() {
        return businessDataParamList;
    }

    public void setBusinessDataParamList(String[] businessDataParamList) {
        this.businessDataParamList = businessDataParamList;
    }
}
