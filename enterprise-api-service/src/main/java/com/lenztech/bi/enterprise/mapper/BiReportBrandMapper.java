//package com.lenztech.bi.enterprise.mapper;
//
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.lenztech.bi.enterprise.entity.BiReportBrand;
//import com.lenztech.bi.enterprise.entity.BiReportBrandExample;
//import org.apache.ibatis.annotations.*;
//
//import java.util.List;
//
//@DS("task")
//public interface BiReportBrandMapper {
//    int countByExample(BiReportBrandExample example);
//
//    int deleteByExample(BiReportBrandExample example);
//
//    @Delete({
//        "delete from bi_report_brand",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    int deleteByPrimaryKey(Integer id);
//
//    @Insert({
//        "insert into bi_report_brand (id, name, ",
//        "task_id, create_time, ",
//        "update_time)",
//        "values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, ",
//        "#{taskId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
//        "#{updateTime,jdbcType=TIMESTAMP})"
//    })
//    int insert(BiReportBrand record);
//
//    int insertSelective(BiReportBrand record);
//
//    List<BiReportBrand> selectByExample(BiReportBrandExample example);
//
//    @Select({
//        "select",
//        "id, name, task_id, create_time, update_time",
//        "from bi_report_brand",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    @ResultMap("BaseResultMap")
//    BiReportBrand selectByPrimaryKey(Integer id);
//
//    int updateByExampleSelective(@Param("record") BiReportBrand record, @Param("example") BiReportBrandExample example);
//
//    int updateByExample(@Param("record") BiReportBrand record, @Param("example") BiReportBrandExample example);
//
//    int updateByPrimaryKeySelective(BiReportBrand record);
//
//    @Update({
//        "update bi_report_brand",
//        "set name = #{name,jdbcType=VARCHAR},",
//          "task_id = #{taskId,jdbcType=VARCHAR},",
//          "create_time = #{createTime,jdbcType=TIMESTAMP},",
//          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    int updateByPrimaryKey(BiReportBrand record);
//}