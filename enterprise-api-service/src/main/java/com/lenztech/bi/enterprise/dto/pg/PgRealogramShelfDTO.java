package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * PG Realogram Shelf 数据 DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel("PG Realogram Shelf 数据 DTO")
public class PgRealogramShelfDTO {

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String banner;

    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码")
    private String storeCode;

    /**
     * 门店类型
     */
    @ApiModelProperty("门店类型")
    private String storeType;

    /**
     * 制造商
     */
    @ApiModelProperty("制造商")
    private String manufacture;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String category;

    /**
     * 品牌名称
     */
    @ApiModelProperty("品牌名称")
    private String brand;

    /**
     * 产品形态
     */
    @ApiModelProperty("产品形态")
    private String productForm;

    /**
     * 产品系列
     */
    @ApiModelProperty("产品系列")
    private String lineUp;

    /**
     * 产品形式
     */
    @ApiModelProperty("产品形式")
    private String productVariant;

    /**
     * EAN产品编码 69码
     */
    @ApiModelProperty("EAN产品编码 69码")
    private String eanCode;

    /**
     * 产品编码
     */
    @ApiModelProperty("产品编码")
    private String productCode;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    @ApiModelProperty("是否是宝洁，0非宝洁/1宝洁")
    private Boolean isPgProductFlag;

    /**
     * 拜访日期时间
     */
    @ApiModelProperty("拜访日期时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date visitDatetime;

    /**
     * 拜访第n次/月
     */
    @ApiModelProperty("拜访第n次/月")
    private Integer visitCycle;

    /**
     * 拜访年份
     */
    @ApiModelProperty("拜访年份")
    private String visitYear;

    /**
     * 拜访月份
     */
    @ApiModelProperty("拜访月份")
    private String visitMonth;

    /**
     * 拜访周数
     */
    @ApiModelProperty("拜访周数")
    private String visitWeek;

    /**
     * group_id
     */
    @ApiModelProperty("group_id")
    private String responseGroupId;

    /**
     * rid
     */
    @ApiModelProperty("rid")
    private String responseId;

    /**
     * pg拼接图url
     */
    @ApiModelProperty("pg拼接图url")
    private String responseImgUrl;

    /**
     * 货架品类-拍摄时选的品类
     */
    @ApiModelProperty("货架品类-拍摄时选的品类")
    private String sceneType;

    /**
     * 货架有几节
     */
    @ApiModelProperty("货架有几节")
    private String maxNumBays;

    /**
     * 货架总层数
     */
    @ApiModelProperty("货架总层数")
    private String maxNumShelves;

    /**
     * 品类货架总长度(cm)
     */
    @ApiModelProperty("品类货架总长度(cm)")
    private BigDecimal totalLengthCat;

    /**
     * 货架总长度(cm)
     */
    @ApiModelProperty("货架总长度(cm)")
    private BigDecimal shelfLengthCm;

    /**
     * photo里sku所在bay的货架
     */
    @ApiModelProperty("photo里sku所在bay的货架")
    private String bayNum;

    /**
     * 第几层货架
     */
    @ApiModelProperty("第几层货架")
    private String shelfLevel;

    /**
     * 第几层堆叠
     */
    @ApiModelProperty("第几层堆叠")
    private String stackLayer;

    /**
     * 水平索引
     */
    @ApiModelProperty("水平索引")
    private String horizontalIdx;

    /**
     * ai识别的坐标
     */
    @ApiModelProperty("ai识别的坐标")
    private String x0Cm;

    /**
     * Bay# 组数
     */
    @ApiModelProperty("Bay# 组数")
    private BigDecimal skuBay;

    /**
     * sku长度(cm)
     */
    @ApiModelProperty("sku长度(cm)")
    private BigDecimal skuLength;

    /**
     * 面位数
     */
    @ApiModelProperty("面位数")
    private BigDecimal facing;

    /**
     * 标记是否库存，0非库存/1库存
     */
    @ApiModelProperty("标记是否库存，0非库存/1库存")
    private String smartAttribute;

    /**
     * SKU个数
     */
    @ApiModelProperty("SKU个数")
    private BigDecimal skuFacing;

    /**
     * facing个数，算多面位
     */
    @ApiModelProperty("facing个数，算多面位")
    private BigDecimal numFacings;

    /**
     * 是否缺货，0-否，1-是
     */
    @ApiModelProperty("是否缺货，0-否，1-是")
    private Boolean oos;

    /**
     * 货架种类
     */
    @ApiModelProperty("货架种类")
    private String shelfType;

    /**
     * sku层级面位数	包含堆叠层
     */
    @ApiModelProperty("sku层级面位数	包含堆叠层")
    private BigDecimal facingWithPosition;

    private String exceDate;

    private String isPosm;
}
