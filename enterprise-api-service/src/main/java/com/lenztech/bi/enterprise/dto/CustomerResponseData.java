package com.lenztech.bi.enterprise.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 客户自定义的通用响应结果
 * @Author:
 * @Date:
 */
@Data
@ApiModel("客户自定义响应结果包装类")
public class CustomerResponseData<T> {

    /**
     * 响应状态码
     */
    @ApiModelProperty("响应状态码")
    private Integer code;

    /**
     * 响应消息
     */
    @ApiModelProperty("响应消息")
    private String msg;

    /**
     * 响应数据
     */
    @ApiModelProperty("响应数据")
    private T data;

    /**
     * 默认构造函数
     */
    public CustomerResponseData() {
    }

    /**
     * 构造函数
     * @param code 状态码
     * @param msg 消息
     * @param data 数据
     */
    public CustomerResponseData(Integer code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 成功响应
     * @param data 数据
     * @return 响应对象
     */
    public static <T> CustomerResponseData<T> success(T data) {
        return new CustomerResponseData<>(200, "success", data);
    }

    /**
     * 失败响应
     * @param code 状态码
     * @param msg 消息
     * @return 响应对象
     */
    public static <T> CustomerResponseData<T> failure(Integer code, String msg) {
        return new CustomerResponseData<>(code, msg, null);
    }

    /**
     * 失败响应（默认状态码）
     * @param msg 消息
     * @return 响应对象
     */
    public static <T> CustomerResponseData<T> failure(String msg) {
        return failure(500, msg);
    }

    /**
     * 默认失败响应
     * @return 响应对象
     */
    public static <T> CustomerResponseData<T> failure() {
        return failure(500, "failure");
    }
}
