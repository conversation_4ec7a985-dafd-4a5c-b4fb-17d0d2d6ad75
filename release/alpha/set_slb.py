#!/usr/bin/env python
# coding=utf-8

import sys

from aliyunsdkcore.client import <PERSON>cs<PERSON>lient
from aliyunsdkslb.request.v20140515.SetVServerGroupAttributeRequest import SetVServerGroupAttributeRequest

AccessId = 'LTAI4FvMzTKjbUqu1M9rCqJC'
AccessSecret = '******************************'
RegionId = 'cn-huhehaote'
server_id = sys.argv[1]
weight = sys.argv[2]


def set_vservergroup(server_id, weight, groupid, groupname, port="80"):
    client = AcsClient(AccessId, AccessSecret, RegionId)
    request = SetVServerGroupAttributeRequest()
    request.set_accept_format('json')
    request.set_VServerGroupId(groupid)
    request.set_VServerGroupName(groupname)
    request.set_BackendServers([{
        "ServerId": server_id,
        "Weight": weight,
        "Port": port
    }])
    response = client.do_action_with_exception(request)
    print(str(response, encoding='utf-8'))


groupid = "rsp-hp32pov7k3thk"
groupname = "mobile.lenztechretail.com"
set_vservergroup(server_id, weight, groupid, groupname)

groupid = "rsp-hp3fmmmvvhjip"
groupname = "mobile.lenztechretail.com"
port = 443
set_vservergroup(server_id, weight, groupid, groupname, port)

# set_vservergroup('i-2ze3ju7pi1p3izn1ngih','100',"rsp-hp3hvn47bu5eg","ferror")

# 调用方式:python3.5 ww_set_slb.py i-2ze3ju7pi1p3izn1ngih 100
