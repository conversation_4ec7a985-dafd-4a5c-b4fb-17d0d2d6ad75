<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.lenztech.bi</groupId>
    <artifactId>bi-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.64</version>
    <name>bi-service</name>
    <description>Demo project for Spring Boot</description>

    <modules>
        <module>enterprise-api-service</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.2.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <java.version>1.8</java.version>
        <spring-cloud.version>Hoxton.SR8</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.5.RELEASE</spring-cloud-alibaba.version>
    </properties>

    <repositories>
        <repository>
            <id>lenz-public</id>
            <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>lenz-public</id>
            <url>http://maven.ppznet.com:8082/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!--私服仓库-->
    <distributionManagement>
        <repository>
            <id>trax-release</id>
            <name>Nexus Release Repository</name>
            <url>http://maven.ppznet.com:8082/repository/trax-release/</url>
        </repository>
        <snapshotRepository>
            <id>lenz-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://maven.ppznet.com:8082/repository/lenz-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
                <nacos.username>test</nacos.username>
                <nacos.password>ppz#2021@test</nacos.password>
            </properties>
        </profile>
        <!--生产环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
                <nacos.username>prod</nacos.username>
                <nacos.password>ppz#2021@prod</nacos.password>
            </properties>
        </profile>
    </profiles>
</project>
