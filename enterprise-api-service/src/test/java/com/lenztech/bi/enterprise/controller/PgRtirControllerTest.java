package com.lenztech.bi.enterprise.controller;

import com.lenztech.bi.enterprise.dto.pg.PgRtirApiRequestDTO;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * PgRtirController 测试类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@SpringBootTest
@ActiveProfiles("test")
public class PgRtirControllerTest {

    @Test
    public void testSyncDataFromApiRequest() {
        // 测试内部分页请求参数序列化
        PgRtirApiRequestDTO requestDTO = new PgRtirApiRequestDTO();
        requestDTO.setCurrent(1);
        requestDTO.setPageSize(50); // 定时同步使用的页面大小
        // 不设置筛选条件，获取所有数据

        String json = JsonUtil.toJsonString(requestDTO);
        System.out.println("内部分页请求参数JSON: " + json);

        // 测试反序列化
        PgRtirApiRequestDTO deserializedDTO = JsonUtil.jsonToPojo(json, PgRtirApiRequestDTO.class);
        System.out.println("反序列化结果: " + JsonUtil.toJsonString(deserializedDTO));
    }

    @Test
    public void testScheduledSyncLogic() {
        // 模拟定时同步的逻辑测试
        System.out.println("=== 定时同步逻辑测试 ===");

        int currentPage = 1;
        int pageSize = 50;
        boolean hasMoreData = true;

        // 模拟分页逻辑
        while (hasMoreData && currentPage <= 3) { // 限制测试只跑3页
            System.out.println("正在处理第" + currentPage + "页，每页" + pageSize + "条");

            // 模拟API调用
            PgRtirApiRequestDTO requestDTO = new PgRtirApiRequestDTO();
            requestDTO.setCurrent(currentPage);
            requestDTO.setPageSize(pageSize);

            System.out.println("请求参数: " + JsonUtil.toJsonString(requestDTO));

            // 模拟判断是否还有更多数据
            if (currentPage >= 3) {
                hasMoreData = false;
                System.out.println("模拟数据获取完毕");
            } else {
                currentPage++;
                System.out.println("继续获取下一页");
            }
        }

        System.out.println("=== 测试完成 ===");
    }

    @Test
    public void testApiResponseParsing() {
        // 模拟第三方API响应
        String mockResponse = """
            {
                "code": 200,
                "success": true,
                "data": {
                    "current": 1,
                    "pageSize": 10,
                    "size": 2,
                    "total": 10816,
                    "records": [
                        {
                            "gtinCode": "06903148047750",
                            "fpcCode": "82204141",
                            "itemStatus": "Inactive",
                            "year": 2019,
                            "sosDate": "2013-12-28",
                            "categoryCode": "206000008",
                            "categoryCn": "个人清洁",
                            "productFormCode": "209000084",
                            "productFormCn": "液体沐浴露",
                            "brandCode": "205000026",
                            "brandCn": "舒肤佳",
                            "variantCode": "207000173",
                            "variantCn": "健康柔肤",
                            "productNameCn": "舒肤佳芦荟水润呵护型沐浴露1升",
                            "length": 11.8,
                            "width": 7.3,
                            "height": 25.6,
                            "fpcImageUrls": [
                                "https://cdn-data-platform.pg.com.cn/product-image/Product-Image-Details/d2e8268398cc467fa2821b9e8ef56173.jpg"
                            ]
                        }
                    ]
                },
                "msg": "操作成功"
            }
            """;

        System.out.println("模拟API响应: " + mockResponse);
    }
}
