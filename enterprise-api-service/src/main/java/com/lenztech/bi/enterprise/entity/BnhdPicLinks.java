package com.lenztech.bi.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-23
 */
public class BnhdPicLinks extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Integer id;

    /**
     * rid
     */
    private String responseId;

    /**
     * 场景
     */
    private String scene;

    /**
     * 图片链接
     */
    private String picUrl;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }
    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BnhdPicLinks{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", scene=" + scene +
        ", picUrl=" + picUrl +
        ",  updateTime=" +  updateTime +
        "}";
    }
}
