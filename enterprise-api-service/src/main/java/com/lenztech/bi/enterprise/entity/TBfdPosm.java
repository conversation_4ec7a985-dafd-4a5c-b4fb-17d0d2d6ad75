package com.lenztech.bi.enterprise.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (TBfdPosm)实体类
 *
 * <AUTHOR>
 * @since 2022-03-14 11:48:44
 */
@Data
public class TBfdPosm implements Serializable {
    private static final long serialVersionUID = 697875187939200361L;

    private Integer id;
    /**
     * 答卷ID
     */
    private String responseId;
    /**
     * sceneID
     */
    private String posmId;
    /**
     * posm编码
     */
    private String productId;
    /**
     * posm名称
     */
    private String productName;
    /**
     * ingId
     */
    private String imageId;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}