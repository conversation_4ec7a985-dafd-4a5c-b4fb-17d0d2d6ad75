//package com.lenztech.bi.enterprise.mapper;
//
//import com.baomidou.dynamic.datasource.annotation.DS;
//import com.lenztech.bi.enterprise.entity.BiReportCategory;
//import com.lenztech.bi.enterprise.entity.BiReportCategoryExample;
//import org.apache.ibatis.annotations.*;
//
//import java.util.List;
//
//@DS("task")
//public interface BiReportCategoryMapper {
//    int countByExample(BiReportCategoryExample example);
//
//    int deleteByExample(BiReportCategoryExample example);
//
//    @Delete({
//        "delete from bi_report_category",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    int deleteByPrimaryKey(Integer id);
//
//    @Insert({
//        "insert into bi_report_category (id, name, ",
//        "task_id, create_time, ",
//        "update_time)",
//        "values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, ",
//        "#{taskId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, ",
//        "#{updateTime,jdbcType=TIMESTAMP})"
//    })
//    int insert(BiReportCategory record);
//
//    int insertSelective(BiReportCategory record);
//
//    List<BiReportCategory> selectByExample(BiReportCategoryExample example);
//
//    @Select({
//        "select",
//        "id, name, task_id, create_time, update_time",
//        "from bi_report_category",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    @ResultMap("BaseResultMap")
//    BiReportCategory selectByPrimaryKey(Integer id);
//
//    int updateByExampleSelective(@Param("record") BiReportCategory record, @Param("example") BiReportCategoryExample example);
//
//    int updateByExample(@Param("record") BiReportCategory record, @Param("example") BiReportCategoryExample example);
//
//    int updateByPrimaryKeySelective(BiReportCategory record);
//
//    @Update({
//        "update bi_report_category",
//        "set name = #{name,jdbcType=VARCHAR},",
//          "task_id = #{taskId,jdbcType=VARCHAR},",
//          "create_time = #{createTime,jdbcType=TIMESTAMP},",
//          "update_time = #{updateTime,jdbcType=TIMESTAMP}",
//        "where id = #{id,jdbcType=INTEGER}"
//    })
//    int updateByPrimaryKey(BiReportCategory record);
//}