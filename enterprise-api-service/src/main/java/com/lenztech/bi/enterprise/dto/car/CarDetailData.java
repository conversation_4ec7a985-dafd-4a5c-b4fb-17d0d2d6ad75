package com.lenztech.bi.enterprise.dto.car;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2020/9/1 11:48
 **/
@Data
public class CarDetailData {
    /**
     * 问卷编号
     */
    @Excel(name = "responseId")
    private String responseId;

    /**
     * 车牌号
     */
    @Excel(name = "licensePlatNumber")
    private String licensePlatNumber;

    /**
     * VIN 码
     */
    @Excel(name = "VINCode")
    private String VINCode;

    /**
     * CJMC
     */
    @Excel(name = "CJMC")
    private String CJMC;

    /**
     * PPMC
     */
    @Excel(name = "PPMC")
    private String PPMC;

    /**
     * CXMC
     */
    @Excel(name = "CXMC")
    private String CXMC;

    /**
     * ND
     */
    @Excel(name = "ND")
    private String ND;

    /**
     * PL
     */
    @Excel(name = "PL")
    private String PL;

    /**
     * 百度识别车名
     */
    @Excel(name = "baiduRecognizeName")
    private String baiduRecognizeName;

    /**
     * 来源渠道
     */
    @Excel(name = "source")
    private String source;

    /**
     * 答卷地址
     */
    @Excel(name = "responseAddress")
    private String responseAddress;

    /**
     * 车牌号地址
     */
    @Excel(name = "licensePlatNumberAddress")
    private String licensePlatNumberAddress;

    /**
     * VIN 码地址
     */
    @Excel(name = "VINCodeAddress")
    private String VINCodeAddress;

}
