package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-14
 */
@TableName("pg_hsm_store_status_bi_report")
public class PgHsmStoreStatusBiReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 门店不同划分，DO 提供，预计9种分类
     */
    @TableField("Store_Tag")
    private String storeTag;

    /**
     * trax 内部门店编码
     */
    @TableField("addressIDnum_new")
    private String addressidnumNew;

    /**
     * 门店信息
     */
    private String distributorId;

    /**
     * 门店信息
     */
    private String distributorStoreId;

    /**
     * 门店信息
     */
    private String storeSeqCode;

    /**
     * 门店信息
     */
    @TableField("Division")
    private String division;

    /**
     * 门店信息
     */
    @TableField("Market")
    private String market;

    /**
     * 门店信息
     */
    @TableField("RD")
    private String rd;

    /**
     * 门店信息
     */
    @TableField("Province")
    private String province;

    /**
     * 门店信息
     */
    @TableField("City")
    private String city;

    /**
     * 门店类型
     */
    @TableField("Store_Type")
    private String storeType;

    /**
     * banner 名称
     */
    @TableField("BANNER")
    private String banner;

    /**
     * 门店地址
     */
    @TableField("Store_Name")
    private String storeName;

    /**
     * 地址信息
     */
    @TableField("Address")
    private String address;

    /**
     * 是否是top banner
     */
    private String topBanner;


    private String dcpId;


    private String dcpStoreId;


    @TableField("Is_dcp_Flag")
    private String isDcpFlag;

    /**
     * 预留字段1
     */
    private String addtionInfo1;

    /**
     * 预留字段2
     */
    private String addtionInfo2;

    /**
     * GE 实际拜访门店
     */
    @TableField("GE_store_visit_date")
    private LocalDate geStoreVisitDate;

    /**
     * 门店当月实际拜访状态
     */
    @TableField("GE_store_visit_status")
    private String geStoreVisitStatus;

    /**
     * 门店的对应图片链接
     */
    @TableField("StoreFront_photo")
    private String storefrontPhoto;

    /**
     * 实地反馈门店状态，具体日期
     */
    @TableField("Filed_feedback_date")
    private LocalDate filedFeedbackDate;

    /**
     * 实地反馈门店状态
     */
    @TableField("Filed_feedback_Status")
    private String filedFeedbackStatus;

    /**
     * 实地反馈评论
     */
    @TableField("Filed_feedback_Comments")
    private String filedFeedbackComments;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 执行月份
     */
    private String month;

    /**
     * 执行日期
     */
    private String date;

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getStoreTag() {
        return storeTag;
    }

    public void setStoreTag(String storeTag) {
        this.storeTag = storeTag;
    }
    public String getAddressidnumNew() {
        return addressidnumNew;
    }

    public void setAddressidnumNew(String addressidnumNew) {
        this.addressidnumNew = addressidnumNew;
    }
    public String getDistributorId() {
        return distributorId;
    }

    public void setDistributorId(String distributorId) {
        this.distributorId = distributorId;
    }
    public String getDistributorStoreId() {
        return distributorStoreId;
    }

    public void setDistributorStoreId(String distributorStoreId) {
        this.distributorStoreId = distributorStoreId;
    }
    public String getStoreSeqCode() {
        return storeSeqCode;
    }

    public void setStoreSeqCode(String storeSeqCode) {
        this.storeSeqCode = storeSeqCode;
    }
    public String getDivision() {
        return division;
    }

    public void setDivision(String division) {
        this.division = division;
    }
    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }
    public String getRd() {
        return rd;
    }

    public void setRd(String rd) {
        this.rd = rd;
    }
    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }
    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }
    public String getBanner() {
        return banner;
    }

    public void setBanner(String banner) {
        this.banner = banner;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
    public String getTopBanner() {
        return topBanner;
    }

    public void setTopBanner(String topBanner) {
        this.topBanner = topBanner;
    }
    public String getDcpId() {
        return dcpId;
    }

    public void setDcpId(String dcpId) {
        this.dcpId = dcpId;
    }
    public String getDcpStoreId() {
        return dcpStoreId;
    }

    public void setDcpStoreId(String dcpStoreId) {
        this.dcpStoreId = dcpStoreId;
    }
    public String getIsDcpFlag() {
        return isDcpFlag;
    }

    public void setIsDcpFlag(String isDcpFlag) {
        this.isDcpFlag = isDcpFlag;
    }
    public String getAddtionInfo1() {
        return addtionInfo1;
    }

    public void setAddtionInfo1(String addtionInfo1) {
        this.addtionInfo1 = addtionInfo1;
    }
    public String getAddtionInfo2() {
        return addtionInfo2;
    }

    public void setAddtionInfo2(String addtionInfo2) {
        this.addtionInfo2 = addtionInfo2;
    }
    public LocalDate getGeStoreVisitDate() {
        return geStoreVisitDate;
    }

    public void setGeStoreVisitDate(LocalDate geStoreVisitDate) {
        this.geStoreVisitDate = geStoreVisitDate;
    }
    public String getGeStoreVisitStatus() {
        return geStoreVisitStatus;
    }

    public void setGeStoreVisitStatus(String geStoreVisitStatus) {
        this.geStoreVisitStatus = geStoreVisitStatus;
    }
    public String getStorefrontPhoto() {
        return storefrontPhoto;
    }

    public void setStorefrontPhoto(String storefrontPhoto) {
        this.storefrontPhoto = storefrontPhoto;
    }
    public LocalDate getFiledFeedbackDate() {
        return filedFeedbackDate;
    }

    public void setFiledFeedbackDate(LocalDate filedFeedbackDate) {
        this.filedFeedbackDate = filedFeedbackDate;
    }
    public String getFiledFeedbackStatus() {
        return filedFeedbackStatus;
    }

    public void setFiledFeedbackStatus(String filedFeedbackStatus) {
        this.filedFeedbackStatus = filedFeedbackStatus;
    }
    public String getFiledFeedbackComments() {
        return filedFeedbackComments;
    }

    public void setFiledFeedbackComments(String filedFeedbackComments) {
        this.filedFeedbackComments = filedFeedbackComments;
    }
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    @Override
    public String toString() {
        return "PgHsmStoreStatusBiReport{" +
            "id=" + id +
            ", storeTag=" + storeTag +
            ", addressidnumNew=" + addressidnumNew +
            ", distributorId=" + distributorId +
            ", distributorStoreId=" + distributorStoreId +
            ", storeSeqCode=" + storeSeqCode +
            ", division=" + division +
            ", market=" + market +
            ", rd=" + rd +
            ", province=" + province +
            ", city=" + city +
            ", storeType=" + storeType +
            ", banner=" + banner +
            ", storeName=" + storeName +
            ", address=" + address +
            ", topBanner=" + topBanner +
            ", dcpId=" + dcpId +
            ", dcpStoreId=" + dcpStoreId +
            ", isDcpFlag=" + isDcpFlag +
            ", addtionInfo1=" + addtionInfo1 +
            ", addtionInfo2=" + addtionInfo2 +
            ", geStoreVisitDate=" + geStoreVisitDate +
            ", geStoreVisitStatus=" + geStoreVisitStatus +
            ", storefrontPhoto=" + storefrontPhoto +
            ", filedFeedbackDate=" + filedFeedbackDate +
            ", filedFeedbackStatus=" + filedFeedbackStatus +
            ", filedFeedbackComments=" + filedFeedbackComments +
            ", createTime=" + createTime +
            ", month=" + month +
        "}";
    }
}
