package com.lenztech.bi.enterprise.dto.pg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PG Optimus查询参数
 *
 */
@Data
@ApiModel("PG Optimus查询参数")
public class PgOptimusQueryParam {

    /**
     * 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     */
    @ApiModelProperty("查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天")
    private String searchDate;

    /**
     * 答卷ID
     */
    @ApiModelProperty("答卷ID")
    private String responseId;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer pageSize;
}
