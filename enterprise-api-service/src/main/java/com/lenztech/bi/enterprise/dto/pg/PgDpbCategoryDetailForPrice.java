package com.lenztech.bi.enterprise.dto.pg;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class PgDpbCategoryDetailForPrice {

    private String categorycode;

    private String taskId;

    private String responseId;

    private String ridStatus;

    private String execDate;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date answerUpdateTime;

    private PgHsmSummary pgHsmSummary = new PgHsmSummary();

    private PgHsmDetailForPrice pgHsmDetail = new PgHsmDetailForPrice();
}
