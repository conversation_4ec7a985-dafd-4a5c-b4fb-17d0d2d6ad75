package com.lenztech.bi.enterprise.utils;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * Description:
 * User: yaoman
 * Date: 2019-09-30
 * Time: 14:07
 */
public class CsvUtils {


    /**
     * 创建csv文件，并返回该文件
     *
     * @param head       文件头
     * @param dataList   文件数据
     * @param outPutPath 存放位置
     * @param filename   文件名
     * @return
     */
    public static File createCSVFile(List<String> head, List<List<String>> dataList, String outPutPath, String filename) {

        File csvFile = null;
        BufferedWriter csvWtriter = null;
        try {
            csvFile = new File(outPutPath + filename + ".csv");
            File parent = csvFile.getParentFile();
            if (parent != null && !parent.exists()) {
                parent.mkdirs();
            }
            csvFile.createNewFile();

            // GB2312使正确读取分隔符","
            csvWtriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(
                    csvFile), StandardCharsets.UTF_8), 1024);
            // 写入文件头部
            writeRow(head, csvWtriter);

            // 写入文件内容
            for (List<String> row : dataList) {
                writeRow(row, csvWtriter);
            }
            csvWtriter.flush();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                csvWtriter.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return csvFile;
    }

    /**
     * 写一行数据方法
     *
     * @param row
     * @param csvWriter
     * @throws IOException
     */
    private static void writeRow(List<String> row, BufferedWriter csvWriter) throws IOException {
        for (String data : row) {
            StringBuffer sb = new StringBuffer();
            String rowStr = sb.append("\"").append(data).append("\",").toString();
            csvWriter.write(rowStr);
        }
        csvWriter.newLine();
    }

    /**
     * filename 下载到客户端后的文件名称
     * filepath 文件的路径 绝对路径和相对路径都可以
     * 下载 文件
     */
    public static void downLoad(String filename, File file, HttpServletResponse response) {

        FileInputStream inStream = null;
        try {
            inStream = new FileInputStream(file);
            byte[] buf = new byte[4096];
            int readLength;
            setResponseHeader(response, filename);
            while (((readLength = inStream.read(buf)) != -1)) {
                response.getOutputStream().write(buf, 0, readLength);
            }
        } catch (Exception e) {
            try {
                //获取OutputStream输出流
                OutputStream outputStream = response.getOutputStream();
                //通过设置响应头控制浏览器以UTF-8的编码显示
                response.setHeader("content-type", "text/html;charset=GBK");
                //将字符转换成字节数组，指定以UTF-8编码进行转换
                byte[] dataByteArr = "下载失败".getBytes("GBK");
                //使用OutputStream流向客户端输出字节数组
                outputStream.write(dataByteArr);
                return;
            } catch (Exception ex) {

            }
        } finally {
            try {
                inStream.close();
                //删除文件
                file.delete();
            } catch (Exception e) {

            }
        }
    }

    /**
     * 设置响应头  文件类型为csv
     */
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {
            response.reset();// 清空输出流
            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename="
                    + new String(fileName.getBytes("GBK"), "8859_1")
                    + ".csv");
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


}
