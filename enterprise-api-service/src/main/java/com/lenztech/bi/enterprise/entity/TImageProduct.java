package com.lenztech.bi.enterprise.entity;

import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */

@Data
public class TImageProduct extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String patchId;

    private String name;

    private String eName;

    private Integer productId;

    private String responseId;

    private String coordinate;

    private String multiCoordinate;

    private String confidence;

    private Integer imageId;

    private String shelfId;

    private String storeId;

    private String imageUrl;

    private Integer isfaceing;

    private Integer inrange;

    private Integer layer;

    private Date createTime;

    private Date updateTime;

    private Integer rank;

    private Integer section;

    private String scene;

    private String stitchCoordinate;
}
