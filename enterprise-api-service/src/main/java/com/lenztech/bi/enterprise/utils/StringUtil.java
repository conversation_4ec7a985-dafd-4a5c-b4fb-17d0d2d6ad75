package com.lenztech.bi.enterprise.utils;


import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * StringUtils 扩展类
 *
 * <AUTHOR>
 * @date 2018-11-26 16:32:40
 */
public class StringUtil extends StringUtils {

    public static boolean isNotBlank(String obj) {
        if (StringUtils.isNotBlank(obj) && !"null".equals(obj)) {
            return true;
        }
        return false;
    }

    /**
     * 版本号比较工具方法
     *
     * @param version1
     * @param version2
     * @return if (version1 > version2), return >0; if (equal), return 0; else return <0
     */
    public static int compareVersion(String version1, String version2) {
        if (version1 == null && version2 == null) {
            return 0;
        } else if (version1 == null) {
            return -1;
        } else if (version2 == null) {
            return 1;
        }
        String[] arr1 = version1.split("[^a-zA-Z0-9]+"), arr2 = version2.split("[^a-zA-Z0-9]+");
        int i1, i2, i3;
        for (int ii = 0, max = Math.min(arr1.length, arr2.length); ii <= max; ii++) {
            if (ii == arr1.length) {
                return ii == arr2.length ? 0 : -1;
            } else if (ii == arr2.length) {
                return 1;
            }
            try {
                i1 = Integer.parseInt(arr1[ii]);
            } catch (Exception x) {
                i1 = Integer.MAX_VALUE;
            }
            try {
                i2 = Integer.parseInt(arr2[ii]);
            } catch (Exception x) {
                i2 = Integer.MAX_VALUE;
            }
            if (i1 != i2) {
                return i1 - i2;
            }
            i3 = arr1[ii].compareTo(arr2[ii]);
            if (i3 != 0) {
                return i3;
            }
        }
        return 0;
    }

    /**
     * 去除换行
     * @param str
     * @return
     */
    public static String replaceBlank(String str) {
        String dest = "";
        if (str != null) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

}
