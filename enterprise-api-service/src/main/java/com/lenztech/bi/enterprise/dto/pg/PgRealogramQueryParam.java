package com.lenztech.bi.enterprise.dto.pg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * PG Realogram 查询参数
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel("PG Realogram 查询参数")
public class PgRealogramQueryParam {

    /**
     * 查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天
     */
    @ApiModelProperty("查询日期 格式：yyyy-MM-dd，不传则默认为当前日期的前一天")
    private String searchDate;

    /**
     * 答卷ID
     */
    @ApiModelProperty("答卷ID")
    private String responseId;

    /**
     * group_id
     */
    @ApiModelProperty("group_id")
    private String responseGroupId;

    /**
     * 门店编码
     */
    @ApiModelProperty("门店编码")
    private String storeCode;

    /**
     * 品类名称
     */
    @ApiModelProperty("品类名称")
    private String category;

    /**
     * 品牌名称
     */
    @ApiModelProperty("品牌名称")
    private String brand;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String skuName;

    /**
     * EAN产品编码
     */
    @ApiModelProperty("EAN产品编码")
    private String eanCode;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    @ApiModelProperty("是否是宝洁，0非宝洁/1宝洁")
    private Boolean isPgProductFlag;

    /**
     * 拜访年份
     */
    @ApiModelProperty("拜访年份")
    private String visitYear;

    /**
     * 拜访月份
     */
    @ApiModelProperty("拜访月份")
    private String visitMonth;

    /**
     * 拜访周数
     */
    @ApiModelProperty("拜访周数")
    private String visitWeek;

    /**
     * 货架品类
     */
    @ApiModelProperty("货架品类")
    private String sceneType;

    /**
     * 是否缺货，0-否，1-是
     */
    @ApiModelProperty("是否缺货，0-否，1-是")
    private Boolean oos;

    /**
     * 页码
     */
    @ApiModelProperty("页码")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @ApiModelProperty("每页大小")
    private Integer pageSize;
}
