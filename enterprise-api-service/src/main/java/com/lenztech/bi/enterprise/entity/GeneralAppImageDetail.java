package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 同界app答卷图片明细
 *
 * <AUTHOR>
 * @since 2022-01-17 14:55:30
 */
@Data
@TableName("general_app_image_detail")
public class GeneralAppImageDetail implements Serializable {
    private static final long serialVersionUID = -39791981003313458L;
    /**
    * 自增id
    */
    private Integer id;
    /**
    * 答卷id
    */
    private String responseId;
    /**
    * 图片id
    */
    private String imageId;
    /**
    * 原图url
    */
    private String imageUrl;
    
    private String recImageUrl;
    /**
    * 图片高度
    */
    private Integer height;
    /**
    * 图片宽度
    */
    private Integer width;
    /**
    * 翻拍 0否 1是
    */
    private Integer remake;
    /**
    * 翻拍置信度分值
    */
    private Double remakeScore;
    /**
    * 商品总数
    */
    private Integer productTotal;
    /**
    * 创建时间
    */
    private Date createTime;

    /**
     * 重复组号
     */
    private Integer repeatGroup;


}