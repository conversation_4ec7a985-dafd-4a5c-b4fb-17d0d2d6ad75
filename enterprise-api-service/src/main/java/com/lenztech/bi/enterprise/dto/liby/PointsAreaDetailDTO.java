package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/14 13:26
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PointsAreaDetailDTO {

    /**
     * 区域名称
     */
    private String area;
    /**
     * 区域城市门店列表
     */
    private List<PointsStoreDetailDTO> pointsStoreDetailDTOS;

}
