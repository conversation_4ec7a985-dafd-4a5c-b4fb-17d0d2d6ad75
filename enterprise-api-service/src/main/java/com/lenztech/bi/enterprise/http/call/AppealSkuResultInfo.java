package com.lenztech.bi.enterprise.http.call;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("申诉SKU信息")
public class AppealSkuResultInfo implements Serializable {

    @ApiModelProperty("分销申诉：0有分销；1无分销")
    private Integer exist;

    @ApiModelProperty("面位数")
    private Double facing;

    @ApiModelProperty("sku代码")
    private String skuCode;

    @ApiModelProperty("分销申诉是否成功")
    private Boolean existVerdict;

    @ApiModelProperty("面位申诉是否成功")
    private Boolean facingVedict;

    public Integer getExist() {
        return exist;
    }

    public void setExist(Integer exist) {
        this.exist = exist;
    }

    public Double getFacing() {
        return facing;
    }

    public void setFacing(Double facing) {
        this.facing = facing;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Boolean getExistVerdict() {
        return existVerdict;
    }

    public void setExistVerdict(Boolean existVerdict) {
        this.existVerdict = existVerdict;
    }

    public Boolean getFacingVedict() {
        return facingVedict;
    }

    public void setFacingVedict(Boolean facingVedict) {
        this.facingVedict = facingVedict;
    }

}
