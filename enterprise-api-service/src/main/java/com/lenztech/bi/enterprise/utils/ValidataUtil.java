package com.lenztech.bi.enterprise.utils;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 验证工具类
 * <AUTHOR>
 */
public class ValidataUtil {

    /**
     * 数值验证:2位小数
     * @param str
     * @return
     */
    public static boolean isNumber(String str) {
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

}
