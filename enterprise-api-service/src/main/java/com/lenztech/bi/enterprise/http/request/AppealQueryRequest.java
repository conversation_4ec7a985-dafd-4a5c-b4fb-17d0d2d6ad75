package com.lenztech.bi.enterprise.http.request;

import com.lenztech.bi.enterprise.validator.annotation.NotEmpty;
import com.lenztech.bi.enterprise.validator.annotation.NotNull;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel("申诉提交请求参数")
public class AppealQueryRequest implements Serializable {

    @ApiModelProperty("企业ID")
    @NotEmpty(message = "companyId must not be empty")
    private String companyId;

    @ApiModelProperty("企业自定义业务参数")
    @NotNull(message = "businessDataParamList must not be empty")
    private String[] businessDataParamList;

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String[] getBusinessDataParamList() {
        return businessDataParamList;
    }

    public void setBusinessDataParamList(String[] businessDataParamList) {
        this.businessDataParamList = businessDataParamList;
    }

}
