package com.lenztech.bi.enterprise.dto.demo;

import lombok.Data;

import java.util.List;

@Data
public class HandleRecordListResp {

    private List<HandleRecord> stockOutRecordList;

    private List<HandleRecord> lowFaceRecordList;

    private List<HandleRecord> priceErrorRecordList;

    @Data
    public static class HandleRecord {
        private String submitId;
        private String imageUrl;
        private String productId;
        private String productName;
        private List<String> questionList;
        private String floorNum;
        private String reportTime;
        private HandleSituation handleSituation;
    }

    @Data
    public static class HandleSituation {
        private String handleWay;
        private String questionContent;
    }

}
