package com.lenztech.bi.enterprise.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenztech.bi.enterprise.utils.JsonUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;


/**
 * @Description: 统一ObjectMapper
 * @Author: zhangjie
 * @Date: 20/11/18 PM2:21
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    @ConditionalOnMissingBean(ObjectMapper.class)
    public ObjectMapper jacksonObjectMapper(Jackson2ObjectMapperBuilder builder) {

        return JsonUtil.getObjectMapper();
    }
}
