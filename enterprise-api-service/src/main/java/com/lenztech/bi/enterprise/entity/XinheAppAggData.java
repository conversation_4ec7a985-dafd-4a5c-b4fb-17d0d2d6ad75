/*
 * Copyright (c) 2022. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
 * Morbi non lorem porttitor neque feugiat blandit. Ut vitae ipsum eget quam lacinia accumsan.
 * Etiam sed turpis ac ipsum condimentum fringilla. Maecenas magna.
 * Proin dapibus sapien vel ante. Aliquam erat volutpat. Pellentesque sagittis ligula eget metus.
 * Vestibulum commodo. Ut rhoncus gravida arcu.
 */

package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * 		date 2022/10/13
 **/
@Data
@TableName("xinhe_app_agg_data")
public class XinheAppAggData {
	
	@TableField(value = "task_id")
	private String taskId;
	
	@TableField(value = "response_id")
	private String responseId;
	
	@TableField(value = "question_id")
	private String questionId;
	
	@TableField(value = "image_id")
	private String imageId;
	
	@TableField(value = "scene")
	private String scene;
	
	@TableField(value = "custom_image_id")
	private String customImageId;
	
	@TableField(value = "image_url")
	private String imageUrl;
	
	@TableField(value = "visit_time")
	private Date visitTime;
	
	@TableField(value = "product_id")
	private Long productId;
	
	@TableField(value = "product_name")
	private String productName;
	
	@TableField(value = "customer_code")
	private String customerCode;
	
	@TableField(value = "product_total")
	private Long productTotal;
	
	@TableField(value = "layer")
	private String layer;
	
	@TableField(value = "layer_count")
	private String layerCount;
	
}
