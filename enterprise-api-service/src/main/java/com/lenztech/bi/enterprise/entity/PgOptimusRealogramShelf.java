package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * SKU级别货架数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pg_optimus_realogram_shelf")
public class PgOptimusRealogramShelf extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 客户名称
     */
    private String banner;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店类型
     */
    private String storeType;

    /**
     * 制造商
     */
    private String manufacture;

    /**
     * 品类名称
     */
    private String category;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 产品形态
     */
    private String productForm;

    /**
     * 产品系列
     */
    private String lineUp;

    /**
     * 产品形式
     */
    private String productVariant;

    /**
     * EAN产品编码 69码
     */
    private String eanCode;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 是否是宝洁，0非宝洁/1宝洁
     */
    private Boolean isPgProductFlag;

    /**
     * 拜访日期时间
     */
    private LocalDateTime visitDatetime;

    /**
     * 拜访第n次/月
     */
    private Integer visitCycle;

    /**
     * 拜访年份
     */
    private String visitYear;

    /**
     * 拜访月份
     */
    private String visitMonth;

    /**
     * 拜访周数
     */
    private String visitWeek;

    /**
     * group_id
     */
    private String responseGroupId;

    /**
     * rid
     */
    private String responseId;

    /**
     * pg拼接图url
     */
    private String responseImgUrl;

    /**
     * 货架品类-拍摄时选的品类
     */
    private String sceneType;

    /**
     * 货架有几节
     */
    private String maxNumBays;

    /**
     * 货架总层数
     */
    private String maxNumShelves;

    /**
     * 品类货架总长度(cm) cat_shelf_length_cm
     */
    private BigDecimal totalLengthCat;

    /**
     * 货架总长度(cm)
     */
    private BigDecimal shelfLengthCm;

    /**
     * photo里sku所在bay的货架
     */
    private String bayNum;

    /**
     * 第几层货架
     */
    private String shelfLevel;

    /**
     * 第几层堆叠
     */
    private String stackLayer;

    /**
     * 水平索引
     */
    private String horizontalIdx;

    /**
     * ai识别的坐标
     */
    private String x0Cm;

    /**
     * Bay# 组数
     */
    private BigDecimal skuBay;

    /**
     * sku长度(cm) linear_cm
     */
    private BigDecimal skuLength;

    /**
     * 面位数 sku层级面位数	只算货架 考虑多支装 这个sku是否被算作了面位，算几个
     */
    private BigDecimal facing;

    /**
     * 标记是否库存，0非库存/1库存
     */
    private String smartAttribute;

    /**
     * SKU个数num_SKU_Count by rid by sku
     */
    private BigDecimal skuFacing;

    /**
     * facing个数，算多面位 by rid
     */
    private BigDecimal numFacings;

    /**
     * 是否缺货，0-否，1-是
     */
    private Boolean oos;

    /**
     * 货架种类
     */
    private String shelfType;

    /**
     * sku层级面位数	包含堆叠层 只算货架 考虑多支装 这个sku是否被算作了面位，算几个
     */
    private BigDecimal facingWithPosition;

    private String exceDate;

    private String isPosm;
}
