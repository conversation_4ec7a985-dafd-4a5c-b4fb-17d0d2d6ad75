package com.lenztech.bi.enterprise.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-06-04
 */
public class HeluxueStoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String responseId;

    private String storeName;

    private Integer picCount;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    public String getResponseId() {
        return responseId;
    }

    public void setResponseId(String responseId) {
        this.responseId = responseId;
    }
    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }
    public Integer getPicCount() {
        return picCount;
    }

    public void setPicCount(Integer picCount) {
        this.picCount = picCount;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "HeluxueStoreRecord{" +
        "id=" + id +
        ", responseId=" + responseId +
        ", storeName=" + storeName +
        ", picCount=" + picCount +
        ", updateTime=" + updateTime +
        "}";
    }
}
