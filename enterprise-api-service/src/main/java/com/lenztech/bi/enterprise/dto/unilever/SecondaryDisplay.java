package com.lenztech.bi.enterprise.dto.unilever;

import com.lenztech.bi.enterprise.entity.UnileverDisplayPosm;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2020/12/23
 * Time: 14:09
 * 类功能:
 */
@Data
public class SecondaryDisplay {

    /**
     * 本品POSM
     */
    private List<SelfProductPosm> selfProductPosmList;

    /**
     * 地堆
     */
    private List<UnileverDisplayPosm> pileOfGroundList;

    /**
     * 其他二级陈列
     */
    private List<UnileverDisplayPosm> otherSecondaryDisplaysList;
}
