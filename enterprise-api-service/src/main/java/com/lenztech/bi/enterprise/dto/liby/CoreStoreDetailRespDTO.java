package com.lenztech.bi.enterprise.dto.liby;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2020/1/13 11:14
 * @since JDK 1.8
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CoreStoreDetailRespDTO {
    /**
     * 扣分次数表
     */
    private List<CoreStoreDetailRespScoreDTO> coreStoreDetailRespScoreDTOS;
    /**
     * 雷达图
     */
    private List<CoreStoreDetailRespVisualizeStoreDTO> coreStoreDetailRespVisualizeStoreDTOS;

}
