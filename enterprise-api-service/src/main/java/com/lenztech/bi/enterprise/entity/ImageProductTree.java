package com.lenztech.bi.enterprise.entity;

/**
 * @Description:
 * @Author: zhangjie
 * @Date: 28/12/18 上午10:45
 */
public class ImageProductTree {

    /**
     * 商品id
     */
    private String id;
    /**
     * SKU code
     */
    private String storeId;

    /**
     * SKU name
     */
    private String name;

    /**
     * SKU 英文名称
     */
    private String eName;

    /**
     * 图片url
     */
    private String imageUrl;

    private String type;

    private String taskId;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String geteName() {
        return eName;
    }

    public void seteName(String eName) {
        this.eName = eName;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
}