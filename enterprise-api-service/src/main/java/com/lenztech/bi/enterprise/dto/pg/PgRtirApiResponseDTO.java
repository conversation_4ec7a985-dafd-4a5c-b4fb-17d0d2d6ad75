package com.lenztech.bi.enterprise.dto.pg;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * PG RTIR 第三方API响应数据DTO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@ApiModel("PG RTIR 第三方API响应数据")
public class PgRtirApiResponseDTO {

    /**
     * 响应码
     */
    @ApiModelProperty("响应码")
    private Integer code;

    /**
     * 是否成功
     */
    @ApiModelProperty("是否成功")
    private Boolean success;

    /**
     * 响应数据
     */
    @ApiModelProperty("响应数据")
    private PgRtirApiDataDTO data;

    /**
     * 响应消息
     */
    @ApiModelProperty("响应消息")
    private String msg;

    /**
     * 分页数据内部类
     */
    @Data
    @ApiModel("分页数据")
    public static class PgRtirApiDataDTO {

        /**
         * 当前页码
         */
        @ApiModelProperty("当前页码")
        private Integer current;

        /**
         * 每页大小
         */
        @ApiModelProperty("每页大小")
        private Integer pageSize;

        /**
         * 当前页记录数
         */
        @ApiModelProperty("当前页记录数")
        private Integer size;

        /**
         * 总记录数
         */
        @ApiModelProperty("总记录数")
        private Integer total;

        /**
         * 记录列表
         */
        @ApiModelProperty("记录列表")
        private List<PgRtirApiRecordDTO> records;
    }
}
