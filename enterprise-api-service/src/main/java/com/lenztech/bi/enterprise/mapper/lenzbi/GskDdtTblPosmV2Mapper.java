package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lenztech.bi.enterprise.dto.gsk.GskBiReport;
import com.lenztech.bi.enterprise.entity.GskDdtTblPosmV2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * gsk-bi指标Mapper
 *
 * <AUTHOR>
 * @date 2021-05-28 15:44:19
 */
@Mapper
@DS("lenzbi")
public interface GskDdtTblPosmV2Mapper extends BaseMapper<GskDdtTblPosmV2> {



}
