package com.lenztech.bi.enterprise.dto.byhealth;

import lombok.Data;

/**
 * 汤臣倍健识别结果集合
 *
 * <AUTHOR>
 * @date 2020-11-10 17:02:02
 */
@Data
public class ByhealthBiResult {

    private String id;

    /**
     * 任务id
     **/
    private String taskId;

    /**
     * 答卷id
     **/
    private String responseId;

    /**
     * 图片id
     **/
    private String imageId;

    /**
     * 图片中所有识别产品计数
     **/
    private Integer facingCount;

    /**
     * 图片中其他产品商品计数
     **/
    private Integer otherFacingCount;

    /**
     * 图片中货架场景所有识别结果计数
     **/
    private Integer hjFacings;

    /**
     * 图片中货架场景其他产品计数
     **/
    private Integer hjOtherFacings;

    /**
     * 图片中端架场景所有识别结果计数
     **/
    private Integer djFacings;

    /**
     * 图片中端架场景其他产品计数
     **/
    private Integer djOtherFacings;

    /**
     * 图片中地堆场景所有识别结果计数
     **/
    private Integer ddFacings;

    /**
     * 图片中地堆场景其他产品计数
     **/
    private Integer ddOtherFacings;

}
