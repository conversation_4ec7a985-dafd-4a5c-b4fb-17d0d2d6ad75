package com.lenztech.bi.enterprise.dto.bi;

import lombok.Data;

/**
 * @Description:图片识别结果
 * @Author: y<PERSON><PERSON><PERSON>g
 * @Date: 15/10/21 AM10:30
 */
@Data
public class ProductImageDetail {

    /**
     * 图片id
     */
    private String imageId;

    /**
     * 图片url
     */
    private String imageUrl;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 坐标
     */
    private String crood;



    /**
     * 是否疑似翻拍0否1是
     */
    private Integer isRemake;

    /**
     * 是否疑似摆拍0否1是
     */
    private Integer isPose;


}
