package com.lenztech.bi.enterprise.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lenztech.bi.enterprise.dto.pg.PgRtirMdDTO;
import com.lenztech.bi.enterprise.entity.PgRtirMd;
import com.lenztech.bi.enterprise.mapper.PgRtirMdMapper;
import com.lenztech.bi.enterprise.service.IPgRtirMdService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * PG RTIR 业务服务类
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class PgRtirService {

    @Autowired
    private PgRtirMdMapper pgRtirMdMapper;

    @Autowired
    private IPgRtirMdService pgRtirMdService;

    /**
     * 分页查询 RTIR 主数据
     * @param gtinCode GTIN编码
     * @param categoryCode 品类编码
     * @param categoryCn 品类名称
     * @param brandCode 品牌编码
     * @param brandCn 品牌名称
     * @param productNameCn 商品名称
     * @param isPgProduct 是否本品
     * @param modeling 是否建模
     * @param isPosm 是否POSM
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public IPage<PgRtirMdDTO> getRtirMdPageList(String gtinCode, String categoryCode, String categoryCn,
                                                String brandCode, String brandCn, String productNameCn,
                                                String isPgProduct, String modeling, String isPosm,
                                                Integer pageNum, Integer pageSize) {
        
        // 设置默认分页参数
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 20;
        }

        // 构建查询条件
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(gtinCode)) {
            queryWrapper.like(PgRtirMd::getGtinCode, gtinCode);
        }
        if (StringUtils.isNotBlank(categoryCode)) {
            queryWrapper.eq(PgRtirMd::getCategoryCode, categoryCode);
        }
        if (StringUtils.isNotBlank(categoryCn)) {
            queryWrapper.like(PgRtirMd::getCategoryCn, categoryCn);
        }
        if (StringUtils.isNotBlank(brandCode)) {
            queryWrapper.eq(PgRtirMd::getBrandCode, brandCode);
        }
        if (StringUtils.isNotBlank(brandCn)) {
            queryWrapper.like(PgRtirMd::getBrandCn, brandCn);
        }
        if (StringUtils.isNotBlank(productNameCn)) {
            queryWrapper.like(PgRtirMd::getProductNameCn, productNameCn);
        }
        if (StringUtils.isNotBlank(isPgProduct)) {
            queryWrapper.eq(PgRtirMd::getIsPgProduct, isPgProduct);
        }
        if (StringUtils.isNotBlank(modeling)) {
            queryWrapper.eq(PgRtirMd::getModeling, modeling);
        }
        if (StringUtils.isNotBlank(isPosm)) {
            queryWrapper.eq(PgRtirMd::getIsPosm, isPosm);
        }

        // 按 GTIN 编码排序
        queryWrapper.orderByAsc(PgRtirMd::getGtinCode);

        // 执行分页查询
        Page<PgRtirMd> page = new Page<>(pageNum, pageSize);
        IPage<PgRtirMd> resultPage = pgRtirMdService.page(page, queryWrapper);

        // 转换为DTO对象
        IPage<PgRtirMdDTO> dtoPage = resultPage.convert(entity -> {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            BeanUtils.copyProperties(entity, dto);
            return dto;
        });

        return dtoPage;
    }

    /**
     * 根据GTIN编码查询单条记录
     * @param gtinCode GTIN编码
     * @return RTIR主数据DTO
     */
    public PgRtirMdDTO getRtirMdByGtinCode(String gtinCode) {
        if (StringUtils.isBlank(gtinCode)) {
            return null;
        }

        PgRtirMd entity = pgRtirMdService.getById(gtinCode);
        if (entity == null) {
            return null;
        }

        PgRtirMdDTO dto = new PgRtirMdDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 获取所有品类列表
     * @return 品类列表
     */
    public List<PgRtirMdDTO> getCategoryList() {
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PgRtirMd::getCategoryCode, PgRtirMd::getCategoryCn)
                   .groupBy(PgRtirMd::getCategoryCode, PgRtirMd::getCategoryCn)
                   .orderByAsc(PgRtirMd::getCategoryCode);

        List<PgRtirMd> entityList = pgRtirMdService.list(queryWrapper);
        List<PgRtirMdDTO> dtoList = new ArrayList<>();
        
        for (PgRtirMd entity : entityList) {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            dto.setCategoryCode(entity.getCategoryCode());
            dto.setCategoryCn(entity.getCategoryCn());
            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * 获取所有品牌列表
     * @return 品牌列表
     */
    public List<PgRtirMdDTO> getBrandList() {
        LambdaQueryWrapper<PgRtirMd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(PgRtirMd::getBrandCode, PgRtirMd::getBrandCn)
                   .groupBy(PgRtirMd::getBrandCode, PgRtirMd::getBrandCn)
                   .orderByAsc(PgRtirMd::getBrandCode);

        List<PgRtirMd> entityList = pgRtirMdService.list(queryWrapper);
        List<PgRtirMdDTO> dtoList = new ArrayList<>();
        
        for (PgRtirMd entity : entityList) {
            PgRtirMdDTO dto = new PgRtirMdDTO();
            dto.setBrandCode(entity.getBrandCode());
            dto.setBrandCn(entity.getBrandCn());
            dtoList.add(dto);
        }
        
        return dtoList;
    }
}
