package com.lenztech.bi.enterprise.mapper.lenzbi;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.lenztech.bi.enterprise.entity.LibyStoreReportPcEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019-11-11 15:09
 * @since JDK 1.8
 */
@Mapper
@DS("lenzbi")
public interface LibyStoreReportPcMapper {
    /**
     * 根据答卷id查询kpi指标
     * @param param
     * @return
     */
    List<LibyStoreReportPcEntity> selectLibyStoreReportPcEntity(Map<String, Object> param);

}
