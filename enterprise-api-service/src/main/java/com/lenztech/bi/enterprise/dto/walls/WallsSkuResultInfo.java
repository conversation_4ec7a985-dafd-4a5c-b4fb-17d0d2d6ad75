package com.lenztech.bi.enterprise.dto.walls;

import com.lenztech.bi.enterprise.entity.HeluxueSkuResult;
import com.lenztech.bi.enterprise.entity.QiangshouSkuResult;
import lombok.Data;

import java.util.List;

/**
 * Created with IntelliJ IDEA.
 * User: sunqingyuan
 * Date: 2021/6/4
 * Time: 10:48
 * 类功能:
 */
@Data
public class WallsSkuResultInfo {

    /**
     * 总分销数
     */
    private Integer totalDistCount;

    /**
     * sku结果集
     */
    private List<HeluxueSkuResult> skuResultList;

}
