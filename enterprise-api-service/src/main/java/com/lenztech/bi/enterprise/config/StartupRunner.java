package com.lenztech.bi.enterprise.config;

import com.lenztech.bi.enterprise.mns.AppealDataFinishListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @Description: 服务启动执行(读取配置)
 * @Author: z<PERSON><PERSON>e
 * @Date: 5/12/18 下午4:14
 */
@Component
@Order(value=1)
public class StartupRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(StartupRunner.class);

    @Autowired
    private AppealDataFinishListener appealDataFinishListener;

    @Override
    public void run(String... args) throws Exception {


        // 启动图像识别完成MNS消息监听器（常驻线程）
        new Thread(){
            @Override
            public void run() {
                try {
                    appealDataFinishListener.consumeMessage();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }.start();


    }
}
