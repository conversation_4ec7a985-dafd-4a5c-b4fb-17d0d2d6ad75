#!/bin/bash
if [ ! -n "$1" ] ;then
    echo "you have not input a branch name,use default branch master!"
    code_env=master
else
    echo "you input branch name is $1"
    code_env=$1
fi

if [ ! -d /data/bi-service ]; then
        echo "代码不存在,从git clone代码,如果clone失败,请确保ssh公钥是否加入到gitee"
        <NAME_EMAIL>:ppz_bj/bi-service.git /data/bi-service || exit -2
else
        echo "配置文件存在,从git pull配置最新文件"
fi
cd /data/lenz_mobile
git checkout .
git checkout ${code_env}
git pull --rebase origin ${code_env}

cd /data/bi-service/release/test

#启动服务—force-recreate
docker-compose -f docker-compose-application.yml pull
docker-compose -f docker-compose-application.yml stop
docker-compose -f docker-compose-application.yml rm -f
docker-compose -f docker-compose-application.yml up -d