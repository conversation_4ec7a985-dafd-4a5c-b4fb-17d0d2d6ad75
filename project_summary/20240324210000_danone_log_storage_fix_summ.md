# 丹能推送日志存储问题修复总结

## 问题描述

使用`DanengPushLogService.saveBatch()`保存日志时出现错误：

```
[ERROR][pool-3-thread-1][se.service.impl.DanengPushLogServiceImpl][saveBatch(59)]批量插入日志失败
org.springframework.jdbc.BadSqlGrammarException: 
### Error updating database.  Cause: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'lenzbi.daneng_push_log' doesn't exist
```

尽管表`daneng_push_log`确实存在，且与`daneng_step2`表在同一个数据库中，但系统无法找到此表。

## 问题分析

错误表明系统试图在`lenzbi`数据库中查找`daneng_push_log`表，但无法找到它。可能的原因：

1. **数据源配置问题**：
   - 服务层可能使用了错误的数据源
   - `DanengPushLogService`的实现类可能配置了指向错误数据库的数据源

2. **表名或Schema问题**：
   - 表名大小写问题
   - 表在不同的数据库Schema中

## 解决方案

参考`PgDpbReportService`中已经正常工作的批量插入方法，我们采用了以下解决方案：

1. **直接使用Mapper层**：
   - 绕过Service层，直接使用Mapper进行数据操作
   - 修改`DanengPushLogMapper`接口，添加`batchInsert`方法

2. **自定义批量插入SQL**：
   - 在`DanengPushLogMapper.xml`中添加批量插入的SQL语句
   - 使用`<foreach>`标签实现批量插入

3. **实现分批处理逻辑**：
   - 在`DanoneService`中实现与`PgDpbReportService`相似的分批处理逻辑
   - 每批最多处理500条记录

## 代码修改

### 1. 添加Mapper方法

```java
@Mapper
public interface DanengPushLogMapper extends BaseMapper<DanengPushLog> {
    /**
     * 批量插入日志记录（参考PgDpbReportMapper的实现）
     */
    int batchInsert(List<DanengPushLog> list);
}
```

### 2. 添加XML配置

```xml
<insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO daneng_push_log (
        store_code, response_id, request_body, response_body, 
        status, status_code, error_message, push_time, 
        response_time, nonce, customer_type, month, 
        inspect_date, create_time
    ) VALUES 
    <foreach collection="list" item="item" separator=",">
        (
        #{item.storeCode}, #{item.responseId}, #{item.requestBody}, #{item.responseBody}, 
        #{item.status}, #{item.statusCode}, #{item.errorMessage}, #{item.pushTime}, 
        #{item.responseTime}, #{item.nonce}, #{item.customerType}, #{item.month}, 
        #{item.inspectDate}, #{item.createTime}
        )
    </foreach>
</insert>
```

### 3. 更新服务实现

```java
/**
 * 批量保存推送日志（参考PgDpbReportService的实现）
 */
private void savePushLogs(List<DanengPushLog> logList) {
    if (logList == null || logList.isEmpty()) {
        return;
    }
    
    try {
        // 使用PgDpbReportService相同的分批处理方式
        int batchSize = BATCH_SIZE;
        for (int i = 0; i < logList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, logList.size());
            List<DanengPushLog> subList = logList.subList(i, end);
            danengPushLogMapper.batchInsert(subList);
        }
        log.info("批量保存日志成功，数量: {}", logList.size());
    } catch (Exception e) {
        log.error("批量插入日志失败", e);
        // 打印具体错误信息，便于调试
        log.error("错误详情: ", e);
    }
}
```

## 技术总结

1. **避免数据源配置错误**：直接使用Mapper层可以避免多数据源配置导致的问题
2. **批量处理优化**：分批处理大量数据可以防止内存溢出和提高性能
3. **统一编程模式**：参考项目中已有的成功实现，保持代码风格一致
4. **增强错误处理**：添加详细的错误日志，便于排查问题

## 后续建议

1. 整理项目中的数据源配置，确保各个模块使用正确的数据源
2. 考虑添加配置项来指定数据库表的位置，避免硬编码
3. 考虑为所有需要批量操作的实体类添加统一的批量处理工具类 