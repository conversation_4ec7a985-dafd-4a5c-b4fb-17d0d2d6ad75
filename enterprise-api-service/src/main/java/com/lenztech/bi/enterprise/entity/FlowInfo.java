package com.lenztech.bi.enterprise.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.lenztech.bi.enterprise.entity.base.BaseEntity;

import java.time.LocalDateTime;

/**
 * <p>
 * 流程主体信息
 * </p>
 *
 * <AUTHOR>
 * @since 2019-08-28
 */
public class FlowInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 流程主键
     */
    @TableId(value = "flow_id", type = IdType.AUTO)
    private Integer flowId;

    /**
     * 流程名称
     */
    private String flowName;

    /**
     * 目前只有两种流程类型：0.申诉流程;1.备案流程
     */
    private Integer flowType;

    /**
     * 关联客户(公司)主键。 该字段和flow_type一起是联合唯一索引
     */
    private Integer companyId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 是否需要再次申诉（0需要再次申诉，1不需要再次申诉）
     */
    private Integer appleagain;

    public Integer getFlowId() {
        return flowId;
    }

    public void setFlowId(Integer flowId) {
        this.flowId = flowId;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public Integer getFlowType() {
        return flowType;
    }

    public void setFlowType(Integer flowType) {
        this.flowType = flowType;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public Integer getAppleagain() {
        return appleagain;
    }

    public void setAppleagain(Integer appleagain) {
        this.appleagain = appleagain;
    }
}
